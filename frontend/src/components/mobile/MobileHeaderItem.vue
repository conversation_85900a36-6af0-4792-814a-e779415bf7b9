<template>
  <header class="mobile-header">
    <!-- 頂部導航欄 -->
    <div class="mobile-nav-top">
      <!-- 左側菜單按鈕 -->
      <button @click="toggleMenu" class="menu-btn">
        <i class="menu-icon"></i>
      </button>
      
      <!-- 中間LOGO -->
      <router-link to="/" class="mobile-logo">
        <img src="@/assets/logo.webp" alt="LOGO" />
      </router-link>
      
      <!-- 右側功能按鈕 -->
      <div class="header-actions">
        <!-- 搜索按鈕 -->
        <button @click="toggleSearch" class="action-btn search-btn">
          <i class="search-icon"></i>
        </button>
        
        <!-- 購物車按鈕 -->
        <router-link to="/check" class="action-btn cart-btn">
          <i class="cart-icon"></i>
          <span v-if="carts.length > 0" class="cart-count">{{ carts.length }}</span>
        </router-link>
        
        <!-- 用戶按鈕 -->
        <button @click="toggleUserMenu" class="action-btn user-btn">
          <i class="user-icon"></i>
        </button>
      </div>
    </div>
    
    <!-- 搜索框 -->
    <div v-show="showSearch" class="mobile-search-bar">
      <div class="search-input-container">
        <input 
          v-model="searchQuery" 
          @keyup.enter="handleSearch"
          type="text" 
          placeholder="搜尋商品..." 
          class="search-input"
        />
        <button @click="handleSearch" class="search-submit-btn">搜尋</button>
      </div>
    </div>
    
    <!-- 側邊菜單遮罩 -->
    <div v-show="showMenu" @click="closeMenu" class="menu-overlay"></div>
    
    <!-- 側邊菜單 -->
    <div :class="['mobile-sidebar', { 'show': showMenu }]">
      <div class="sidebar-header">
        <div class="user-info" v-if="isUser">
          <div class="user-avatar">
            <i class="user-icon"></i>
          </div>
          <div class="user-name">{{ clientData.name || '會員' }}</div>
        </div>
        <div v-else class="login-prompt">
          <router-link to="/login" @click="closeMenu" class="login-link">
            登入 / 註冊
          </router-link>
        </div>
        <button @click="closeMenu" class="close-btn">×</button>
      </div>
      
      <nav class="sidebar-nav">
        <router-link to="/" @click="closeMenu" class="nav-item">
          <i class="home-icon"></i>首頁
        </router-link>
        <router-link to="/products" @click="closeMenu" class="nav-item">
          <i class="products-icon"></i>所有商品
        </router-link>
        <router-link to="/brands" @click="closeMenu" class="nav-item">
          <i class="brands-icon"></i>品牌專區
        </router-link>
        
        <!-- 商品分類 -->
        <div class="category-section">
          <div class="section-title">商品分類</div>
          <div v-for="category in classSet" :key="category.id" class="category-item">
            <router-link 
              :to="`/products?category=${category.id}`" 
              @click="closeMenu"
              class="category-link"
            >
              {{ category.name }}
            </router-link>
          </div>
        </div>
        
        <!-- 會員功能 -->
        <div v-if="isUser" class="member-section">
          <div class="section-title">會員專區</div>
          <router-link to="/account" @click="closeMenu" class="nav-item">
            <i class="account-icon"></i>個人資料
          </router-link>
          <router-link to="/products?sort=社員專區" @click="closeMenu" class="nav-item">
            <i class="member-icon"></i>社員專區
          </router-link>
          <button @click="handleLogout" class="nav-item logout-btn">
            <i class="logout-icon"></i>登出
          </button>
        </div>
        
        <!-- 其他頁面 -->
        <div class="other-section">
          <div class="section-title">其他</div>
          <router-link to="/news" @click="closeMenu" class="nav-item">
            <i class="news-icon"></i>最新消息
          </router-link>
          <router-link to="/service" @click="closeMenu" class="nav-item">
            <i class="service-icon"></i>客服中心
          </router-link>
          <router-link to="/aboutUs" @click="closeMenu" class="nav-item">
            <i class="about-icon"></i>關於我們
          </router-link>
        </div>
      </nav>
    </div>
    
    <!-- 用戶快速菜單 -->
    <div v-show="showUserMenu" @click="closeUserMenu" class="user-menu-overlay"></div>
    <div :class="['user-dropdown', { 'show': showUserMenu }]">
      <div v-if="isUser" class="user-menu-content">
        <router-link to="/account" @click="closeUserMenu" class="user-menu-item">
          個人資料
        </router-link>
        <router-link to="/products?sort=社員專區" @click="closeUserMenu" class="user-menu-item">
          社員專區
        </router-link>
        <button @click="handleLogout" class="user-menu-item logout">
          登出
        </button>
      </div>
      <div v-else class="user-menu-content">
        <router-link to="/login" @click="closeUserMenu" class="user-menu-item">
          登入 / 註冊
        </router-link>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import { message } from 'ant-design-vue'

const router = useRouter()
const handelPinia = piniaStores()
const { classSet, carts, isUser, clientData } = storeToRefs(handelPinia)

// 控制狀態
const showMenu = ref(false)
const showSearch = ref(false)
const showUserMenu = ref(false)
const searchQuery = ref('')

// 切換菜單
const toggleMenu = () => {
  showMenu.value = !showMenu.value
  if (showMenu.value) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

const closeMenu = () => {
  showMenu.value = false
  document.body.style.overflow = ''
}

// 切換搜索
const toggleSearch = () => {
  showSearch.value = !showSearch.value
}

// 切換用戶菜單
const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const closeUserMenu = () => {
  showUserMenu.value = false
}

// 處理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push(`/products?search=${encodeURIComponent(searchQuery.value.trim())}`)
    showSearch.value = false
    searchQuery.value = ''
  }
}

// 處理登出
const handleLogout = () => {
  handelPinia.logout()
  closeMenu()
  closeUserMenu()
  message.success('已成功登出')
  router.push('/')
}

// 點擊外部關閉菜單
const handleClickOutside = (event) => {
  if (showUserMenu.value && !event.target.closest('.user-dropdown') && !event.target.closest('.user-btn')) {
    closeUserMenu()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
  document.body.style.overflow = ''
})
</script>

<style scoped>
/* 手機版Header樣式 */
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-nav-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  height: 56px;
}

.menu-btn, .action-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.menu-btn:hover, .action-btn:hover {
  background-color: #f5f5f5;
}

.mobile-logo img {
  height: 32px;
  width: auto;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.cart-btn {
  position: relative;
}

.cart-count {
  position: absolute;
  top: 0;
  right: 0;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  font-size: 10px;
  padding: 2px 5px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 搜索框樣式 */
.mobile-search-bar {
  padding: 8px 16px;
  border-top: 1px solid #eee;
  background: #f9f9f9;
}

.search-input-container {
  display: flex;
  gap: 8px;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-submit-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* 側邊菜單樣式 */
.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

.mobile-sidebar {
  position: fixed;
  top: 0;
  left: -300px;
  width: 280px;
  height: 100vh;
  background: white;
  z-index: 1002;
  transition: left 0.3s ease;
  overflow-y: auto;
}

.mobile-sidebar.show {
  left: 0;
}

.sidebar-header {
  padding: 20px 16px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-prompt {
  text-align: center;
}

.login-link {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 5px;
}

.sidebar-nav {
  padding: 16px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  text-decoration: none;
  color: #333;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
}

.nav-item:hover {
  background-color: #f5f5f5;
}

.section-title {
  padding: 8px 16px;
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 16px;
}

.category-section, .member-section, .other-section {
  border-top: 1px solid #eee;
  margin-top: 8px;
}

.category-item {
  padding: 0 16px;
}

.category-link {
  display: block;
  padding: 8px 0;
  color: #666;
  text-decoration: none;
  font-size: 14px;
}

.category-link:hover {
  color: #007bff;
}

.logout-btn {
  color: #ff4757;
}

/* 用戶下拉菜單 */
.user-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.user-dropdown {
  position: absolute;
  top: 56px;
  right: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  min-width: 150px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  pointer-events: none;
  z-index: 1000;
}

.user-dropdown.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.user-menu-item {
  display: block;
  padding: 12px 16px;
  color: #333;
  text-decoration: none;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-menu-item:hover {
  background-color: #f5f5f5;
}

.user-menu-item.logout {
  color: #ff4757;
  border-top: 1px solid #eee;
}

/* 圖標樣式 */
.menu-icon, .search-icon, .cart-icon, .user-icon,
.home-icon, .products-icon, .brands-icon, .account-icon,
.member-icon, .logout-icon, .news-icon, .service-icon, .about-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.menu-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 6h16M4 12h16M4 18h16'/%3E%3C/svg%3E");
}

.search-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'/%3E%3C/svg%3E");
}

.cart-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.39.39-.39 1.024 0 1.414L6 17h11m-1 0a2 2 0 104 0m-6 0a2 2 0 104 0'/%3E%3C/svg%3E");
}

.user-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'/%3E%3C/svg%3E");
}

/* 響應式設計 */
@media (max-width: 480px) {
  .mobile-sidebar {
    width: 100%;
    left: -100%;
  }
  
  .mobile-sidebar.show {
    left: 0;
  }
  
  .user-dropdown {
    right: 8px;
  }
}
</style> 