<template>
  <footer class="mobile-footer">
    <!-- 底部導航欄 -->
    <nav class="mobile-bottom-nav">
      <router-link to="/" class="nav-tab" :class="{ active: $route.path === '/' }">
        <i class="home-icon"></i>
        <span class="nav-label">首頁</span>
      </router-link>
      
      <router-link to="/products" class="nav-tab" :class="{ active: $route.path === '/products' }">
        <i class="products-icon"></i>
        <span class="nav-label">商品</span>
      </router-link>
      
      <router-link to="/brands" class="nav-tab" :class="{ active: $route.path === '/brands' }">
        <i class="brands-icon"></i>
        <span class="nav-label">品牌</span>
      </router-link>
      
      <router-link to="/check" class="nav-tab cart-tab" :class="{ active: $route.path === '/check' }">
        <div class="cart-icon-container">
          <i class="cart-icon"></i>
          <span v-if="carts.length > 0" class="cart-badge">{{ carts.length }}</span>
        </div>
        <span class="nav-label">購物車</span>
      </router-link>
      
      <router-link 
        :to="isUser ? '/account' : '/login'" 
        class="nav-tab" 
        :class="{ active: $route.path === '/account' || $route.path === '/login' }"
      >
        <i class="user-icon"></i>
        <span class="nav-label">{{ isUser ? '我的' : '登入' }}</span>
      </router-link>
    </nav>
    
    <!-- 公司資訊 -->
    <div class="footer-info">
      <div class="company-info">
        <p class="company-name">庫樂克國際有限公司</p>
        <div class="info-links">
          <router-link to="/aboutUs" class="info-link">關於我們</router-link>
          <span class="separator">|</span>
          <router-link to="/privacy" class="info-link">隱私政策</router-link>
          <span class="separator">|</span>
          <router-link to="/shoppingNotice" class="info-link">購物須知</router-link>
          <span class="separator">|</span>
          <router-link to="/transportNotice" class="info-link">運送說明</router-link>
        </div>
        <div class="contact-info">
          <p class="contact-item">客服專線：02-1234-5678</p>
          <p class="contact-item">營業時間：週一至週五 09:00-18:00</p>
        </div>
        <p class="copyright">© 2024 庫樂克國際有限公司. All rights reserved.</p>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'

const handelPinia = piniaStores()
const { carts, isUser } = storeToRefs(handelPinia)
</script>

<style scoped>
/* 手機版Footer樣式 */
.mobile-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

/* 底部導航欄 */
.mobile-bottom-nav {
  display: flex;
  background: #fff;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.nav-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 4px;
  text-decoration: none;
  color: #666;
  transition: all 0.2s ease;
  position: relative;
  min-height: 60px;
}

.nav-tab:hover {
  background-color: #f5f5f5;
}

.nav-tab.active {
  color: #007bff;
  background-color: #f0f8ff;
}

.nav-label {
  font-size: 11px;
  margin-top: 4px;
  text-align: center;
  line-height: 1.2;
}

.cart-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  font-size: 10px;
  padding: 2px 5px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* 公司資訊區域 */
.footer-info {
  background: #f8f9fa;
  padding: 16px;
  border-top: 1px solid #e0e0e0;
  display: none; /* 預設隱藏，可根據需要顯示 */
}

.company-info {
  text-align: center;
  max-width: 400px;
  margin: 0 auto;
}

.company-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-links {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.info-link {
  color: #666;
  text-decoration: none;
  font-size: 12px;
  transition: color 0.2s;
}

.info-link:hover {
  color: #007bff;
}

.separator {
  color: #ccc;
  font-size: 12px;
}

.contact-info {
  margin-bottom: 12px;
}

.contact-item {
  font-size: 11px;
  color: #666;
  margin: 4px 0;
}

.copyright {
  font-size: 10px;
  color: #999;
  margin: 0;
}

/* 圖標樣式 */
.home-icon, .products-icon, .brands-icon, .cart-icon, .user-icon {
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.home-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6'/%3E%3C/svg%3E");
}

.products-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10'/%3E%3C/svg%3E");
}

.brands-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z'/%3E%3C/svg%3E");
}

.cart-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.39.39-.39 1.024 0 1.414L6 17h11m-1 0a2 2 0 104 0m-6 0a2 2 0 104 0'/%3E%3C/svg%3E");
}

.user-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'/%3E%3C/svg%3E");
}

/* 響應式設計 */
@media (max-width: 480px) {
  .nav-tab {
    padding: 6px 2px;
    min-height: 56px;
  }
  
  .nav-label {
    font-size: 10px;
  }
  
  .home-icon, .products-icon, .brands-icon, .cart-icon, .user-icon {
    width: 20px;
    height: 20px;
  }
  
  .info-links {
    flex-direction: column;
    gap: 4px;
  }
  
  .separator {
    display: none;
  }
}

/* 當需要顯示完整footer資訊時 */
.mobile-footer.show-info .footer-info {
  display: block;
}

/* 在特定頁面可能需要調整bottom nav的位置 */
.mobile-footer.transparent {
  background: transparent;
}

.mobile-footer.transparent .mobile-bottom-nav {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}
</style> 