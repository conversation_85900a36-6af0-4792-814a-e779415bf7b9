import { createRouter, createWebHistory } from 'vue-router'
import { message } from 'ant-design-vue'

// Token 驗證函數 (與電腦版相同)
const validateToken = () => {
  const token = localStorage.getItem('token')
  const isLogin = localStorage.getItem('isLogin')
  
  if (!token || !isLogin || token === 'null' || token === 'undefined') {
    return false
  }
  
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      console.warn('🔐 Token 格式無效')
      return false
    }
    
    const payload = JSON.parse(atob(parts[1]))
    if (payload.exp) {
      const currentTime = Math.floor(Date.now() / 1000)
      if (payload.exp < currentTime) {
        console.warn('🔐 Token 已過期')
        return false
      }
    }
    
    return true
  } catch (error) {
    console.error('🔐 Token 驗證失敗:', error)
    return false
  }
}

// 清除無效的登入狀態
const clearInvalidAuth = () => {
  const authKeys = ['token', 'isLogin', 'user', 'userData', 'belongCp']
  authKeys.forEach(key => {
    localStorage.removeItem(key)
  })
  console.log('✅ 已清除無效的認證資料')
}

const mobileRouter = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to, from, savedPosition) {
    // 手機版優化的滾動行為
    if (to.path === '/product') {
      return { top: 0, behavior: 'instant' }
    }
    
    if (from.path === '/product' && to.path === '/products' && savedPosition) {
      return { ...savedPosition, behavior: 'instant' }
    }
    
    const immediateScrollPages = ['/check', '/login', '/account']
    if (immediateScrollPages.includes(to.path)) {
      return { top: 0, behavior: 'instant' }
    }
    
    if (savedPosition && from.path !== to.path) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ ...savedPosition, behavior: 'smooth' })
        }, 100)
      })
    }
    
    return { top: 0, behavior: 'smooth' }
  },
  routes: [
    {
      path: '/',
      name: 'mobile-home',
      component: () => import('../views/mobile/MobileHomeView.vue'),
    },
    {
      path: '/aboutUs',
      name: 'mobile-about',
      component: () => import('../views/mobile/inform/MobileAboutView.vue'),
    },
    {
      path: '/shoppingNotice',
      name: 'mobile-shopping-notice',
      component: () => import('../views/mobile/inform/MobileShoppingNotice.vue'),
    },
    {
      path: '/transportNotice',
      name: 'mobile-transport-notice',
      component: () => import('../views/mobile/inform/MobileTransportNotice.vue'),
    },
    {
      path: '/privacy',
      name: 'mobile-privacy',
      component: () => import('../views/mobile/inform/MobilePrivacyView.vue'),
    },
    {
      path: '/login',
      name: 'mobile-login',
      component: () => import('../views/mobile/MobileLoginView.vue'),
    },
    {
      path: '/account',
      name: 'mobile-account',
      component: () => import('../views/mobile/MobileAccountView.vue'),
      meta: { isLogin: true },
    },
    {
      path: '/news',
      name: 'mobile-news',
      component: () => import('../views/mobile/MobileNewsView.vue'),
    },
    {
      path: '/service',
      name: 'mobile-service',
      component: () => import('../views/mobile/MobileServiceView.vue'),
    },
    {
      path: '/products',
      name: 'mobile-products',
      component: () => import('../views/mobile/MobileProductsView.vue'),
    },
    {
      path: '/product',
      name: 'mobile-product',
      component: () => import('../views/mobile/MobileProductInfo.vue'),
    },
    {
      path: '/check',
      name: 'mobile-check',
      component: () => import('../views/mobile/MobileCheckView.vue'),
      meta: { isLogin: true },
    },
    {
      path: '/brands',
      name: 'mobile-brands',
      component: () => import('../views/mobile/MobileBrandsView.vue'),
    },
    {
      path: '/brands/all',
      name: 'mobile-all-brands',
      component: () => import('../views/mobile/MobileBrandDetailView.vue'),
    },
    {
      path: '/brand/:id',
      name: 'mobile-brand-detail',
      component: () => import('../views/mobile/MobileBrandDetailView.vue'),
    },
    {
      path: '/member',
      redirect: '/account',
      meta: { isLogin: true },
    },
    {
      path: '/admin',
      redirect: 'http://localhost:5173/',
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/',
    },
  ],
})

// 路由守衛 (與電腦版相同邏輯)
mobileRouter.beforeEach((to, from, next) => {
  if (to.meta.isLogin) {
    const isValidLogin = validateToken()
    
    if (!isValidLogin) {
      console.warn('🔐 手機版路由守衛：Token無效或已過期，重導向到登入頁')
      clearInvalidAuth()
      
      if (from.path !== '/login') {
        message.warning('登入已過期，請重新登入')
      }
      
      return next('/login')
    }
  }

  if (to.path === '/products' && to.query.sort === '社員專區') {
    const isValidLogin = validateToken()
    
    if (!isValidLogin) {
      console.warn('🔐 手機版路由守衛：訪問社員專區但Token無效，重導向到登入頁')
      clearInvalidAuth()
      
      if (from.path !== '/login') {
        message.warning('請先登入以訪問社員專區')
      }
      
      return next('/login')
    }
  }

  next()
})

export default mobileRouter 