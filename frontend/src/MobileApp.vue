<template>
  <div id="mobile-app">
    <MobileHeaderItem />
    <main class="mobile-main-content">
      <router-view />
    </main>
    <MobileFooterItem />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import MobileHeaderItem from '@/components/mobile/MobileHeaderItem.vue'
import MobileFooterItem from '@/components/mobile/MobileFooterItem.vue'

// 初始化 Pinia store (與電腦版相同)
const handelPinia = piniaStores()
const { classSet, isUser, carts, total } = storeToRefs(handelPinia)

// 在應用啟動時預加載分類數據並檢查登入狀態
onMounted(async () => {
  try {
    await handelPinia.getClassSet()
    
    const token = localStorage.getItem('token')
    const isLogin = localStorage.getItem('isLogin')
    
    if ((!token || !isLogin) && carts.value.length > 0) {
      handelPinia.clearCart()
    } else {
      handelPinia.initUser(true)
    }
  } catch (error) {
    console.error('MobileApp: 預加載數據失敗', error)
  }
})
</script>

<style scoped>
/* 手機版全局樣式 */
#mobile-app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden;
  font-family: "Microsoft JhengHei", "PingFang TC", "Heiti TC", sans-serif;
}

/* 手機版主內容區域 */
.mobile-main-content {
  padding-top: 60px; /* 手機版 header 高度 */
  padding-bottom: 70px; /* 手機版 footer 高度 */
  flex: 1;
  width: 100%;
  position: relative;
  z-index: 1;
  min-height: calc(100vh - 130px);
}

/* 確保在手機版下的響應式設計 */
@media (max-width: 768px) {
  .mobile-main-content {
    padding-top: 56px;
    padding-bottom: 65px;
  }
}

/* 防止水平滾動 */
* {
  box-sizing: border-box;
}
</style> 