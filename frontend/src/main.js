import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import Antd, { message } from 'ant-design-vue'
import App from './App.vue'
import router from './router'
import mobileRouter from './router/mobile'
import { isMobileDevice } from './utils/deviceDetection'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-tw'

// 先引入 Ant Design 樣式，再引入自定義樣式，確保自定義樣式優先級更高
import 'ant-design-vue/dist/reset.css'
import './assets/main.css'
import './assets/css/rich-text.css'
import './assets/styles/rich-text.css' // 確保富文本樣式能覆蓋 Ant Design 樣式
import './assets/styles/highlights.css' // 添加 highlights 樣式，確保可以覆蓋 Ant Design 樣式

// 設置dayjs為中文繁體
dayjs.locale('zh-tw')

// 根據設備類型選擇路由
const currentRouter = isMobileDevice() ? mobileRouter : router

console.log(`🚀 應用啟動 - 設備類型: ${isMobileDevice() ? '手機' : '電腦'}`)
console.log(`📱 使用路由: ${isMobileDevice() ? 'Mobile Router' : 'Desktop Router'}`)

const app = createApp(App)
const pinia = createPinia()

// 配置 Pinia 持久化插件
pinia.use(piniaPluginPersistedstate)

// 全局配置 antd message
message.config({
  top: `100px`,
  duration: 1.5,
  maxCount: 3,
});

// 使用插件
app.use(pinia)
app.use(currentRouter) // 使用動態選擇的路由
app.use(Antd)

// 提供全局的路由實例給應用使用
app.provide('router', currentRouter)
app.provide('isMobile', isMobileDevice())

// 將路由實例掛載到全局屬性，方便調試
app.config.globalProperties.$currentRouter = currentRouter
app.config.globalProperties.$isMobile = isMobileDevice()

// 在開發環境下提供更多調試信息
if (import.meta.env.DEV) {
  window.__APP_ROUTER__ = currentRouter
  window.__IS_MOBILE__ = isMobileDevice()
  
  // 添加路由變化監聽器用於調試
  currentRouter.beforeEach((to, from, next) => {
    console.log(`🧭 路由導航: ${from.path} → ${to.path}`)
    next()
  })
}

// 設備變化時的路由切換處理
// 注意：這裡只是提供概念，實際的設備切換應該通過App.vue中的組件切換來處理
let lastDeviceType = isMobileDevice()

const handleRouterSwitch = () => {
  const currentDeviceType = isMobileDevice()
  if (currentDeviceType !== lastDeviceType) {
    console.log(`🔄 檢測到設備類型變化: ${lastDeviceType ? '手機' : '電腦'} → ${currentDeviceType ? '手機' : '電腦'}`)
    lastDeviceType = currentDeviceType
    
    // 這裡可以添加額外的設備切換邏輯
    // 例如清除特定的緩存、重置某些狀態等
  }
}

// 監聽窗口大小變化
let resizeTimer = null
window.addEventListener('resize', () => {
  clearTimeout(resizeTimer)
  resizeTimer = setTimeout(handleRouterSwitch, 300)
})

// 掛載應用
app.mount('#app')