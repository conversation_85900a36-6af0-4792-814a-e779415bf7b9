<template>
  <div class="mobile-shopping-notice">
    <div class="page-header">
      <h1>購物須知</h1>
    </div>
    
    <div class="notice-content">
      <div class="notice-section">
        <h2>訂購流程</h2>
        <ol>
          <li>選擇商品並加入購物車</li>
          <li>前往購物車確認商品</li>
          <li>填寫收件資訊</li>
          <li>選擇付款方式</li>
          <li>確認訂單並完成付款</li>
        </ol>
      </div>
      
      <div class="notice-section">
        <h2>付款方式</h2>
        <ul>
          <li>信用卡付款</li>
          <li>ATM轉帳</li>
          <li>貨到付款</li>
          <li>超商取貨付款</li>
        </ul>
      </div>
      
      <div class="notice-section">
        <h2>配送方式</h2>
        <ul>
          <li>宅配到府：1-3個工作天</li>
          <li>超商取貨：2-4個工作天</li>
          <li>門市自取：隔日可取</li>
        </ul>
      </div>
      
      <div class="notice-section">
        <h2>退換貨政策</h2>
        <p>商品到貨後7天內，在未使用的狀況下可申請退換貨。</p>
        <p>退換貨需保持商品原包裝完整，並附上購買憑證。</p>
      </div>
      
      <div class="notice-section">
        <h2>注意事項</h2>
        <ul>
          <li>請確認收件資訊正確，避免配送延誤</li>
          <li>特價商品不適用退換貨服務</li>
          <li>大型商品配送時間另行安排</li>
          <li>偏遠地區可能需額外運費</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
// 此頁面為靜態內容，不需要額外的邏輯
</script>

<style scoped>
.mobile-shopping-notice {
  background: #f8f9fa;
  min-height: calc(100vh - 130px);
}

.page-header {
  background: white;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.page-header h1 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.notice-content {
  padding: 16px;
}

.notice-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
}

.notice-section h2 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px;
  color: #333;
}

.notice-section p {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  margin-bottom: 12px;
}

.notice-section p:last-child {
  margin-bottom: 0;
}

.notice-section ul,
.notice-section ol {
  margin: 0;
  padding-left: 20px;
}

.notice-section li {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  margin-bottom: 8px;
}

.notice-section li:last-child {
  margin-bottom: 0;
}
</style> 