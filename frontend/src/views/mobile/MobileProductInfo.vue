<template>
  <div class="mobile-product-info">
    <div v-if="product" class="product-detail">
      <div class="product-image">
        <img :src="product.image || '/no-image.jpg'" :alt="product.name" />
      </div>
      
      <div class="product-content">
        <h1 class="product-title">{{ product.name }}</h1>
        <div class="product-price">NT$ {{ formatPrice(product.price) }}</div>
        
        <div class="product-description">
          {{ product.description || '暫無商品描述' }}
        </div>
        
        <div class="product-actions">
          <button @click="addToCart" class="add-cart-btn">
            加入購物車
          </button>
        </div>
      </div>
    </div>
    
    <div v-else class="loading">
      載入中...
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'

const route = useRoute()
const product = ref(null)

const formatPrice = (price) => {
  return new Intl.NumberFormat('zh-TW').format(price)
}

const addToCart = () => {
  message.success('已加入購物車')
}

onMounted(() => {
  // 模擬載入商品數據
  const productId = route.query.id
  product.value = {
    id: productId,
    name: `商品 ${productId}`,
    price: 1000,
    image: '/no-image.jpg',
    description: '這是一個很棒的商品。'
  }
})
</script>

<style scoped>
.mobile-product-info {
  background: #f8f9fa;
  min-height: calc(100vh - 130px);
}

.product-detail {
  background: white;
}

.product-image {
  width: 100%;
  aspect-ratio: 1;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-content {
  padding: 20px;
}

.product-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 12px;
}

.product-price {
  font-size: 24px;
  font-weight: 600;
  color: #dc3545;
  margin-bottom: 16px;
}

.product-description {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  margin-bottom: 24px;
}

.add-cart-btn {
  width: 100%;
  padding: 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}
</style> 