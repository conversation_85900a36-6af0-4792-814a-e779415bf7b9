<template>
  <div class="mobile-check">
    <div class="cart-header">
      <h1 class="page-title">購物車</h1>
    </div>
    
    <div v-if="cartItems.length > 0" class="cart-content">
      <div class="cart-items">
        <div v-for="item in cartItems" :key="item.id" class="cart-item">
          <div class="item-image">
            <img :src="item.image || '/no-image.jpg'" :alt="item.name" />
          </div>
          <div class="item-info">
            <h3 class="item-name">{{ item.name }}</h3>
            <div class="item-price">NT$ {{ formatPrice(item.price) }}</div>
            <div class="item-quantity">
              <button @click="decreaseQuantity(item)" class="quantity-btn">-</button>
              <span class="quantity">{{ item.quantity }}</span>
              <button @click="increaseQuantity(item)" class="quantity-btn">+</button>
            </div>
          </div>
          <button @click="removeItem(item)" class="remove-btn">×</button>
        </div>
      </div>
      
      <div class="cart-summary">
        <div class="summary-row">
          <span>小計</span>
          <span>NT$ {{ formatPrice(subtotal) }}</span>
        </div>
        <div class="summary-row">
          <span>運費</span>
          <span>NT$ {{ formatPrice(shipping) }}</span>
        </div>
        <div class="summary-row total">
          <span>總計</span>
          <span>NT$ {{ formatPrice(total) }}</span>
        </div>
      </div>
      
      <div class="checkout-actions">
        <button @click="proceedToCheckout" class="checkout-btn">
          前往結帳
        </button>
      </div>
    </div>
    
    <div v-else class="empty-cart">
      <div class="empty-icon">🛒</div>
      <p class="empty-text">您的購物車是空的</p>
      <button @click="goShopping" class="shop-btn">去購物</button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

const router = useRouter()

const cartItems = ref([
  {
    id: 1,
    name: '商品1',
    price: 1000,
    quantity: 2,
    image: '/no-image.jpg'
  },
  {
    id: 2,
    name: '商品2',
    price: 2000,
    quantity: 1,
    image: '/no-image.jpg'
  }
])

const shipping = ref(100)

const subtotal = computed(() => {
  return cartItems.value.reduce((sum, item) => sum + (item.price * item.quantity), 0)
})

const total = computed(() => {
  return subtotal.value + shipping.value
})

const formatPrice = (price) => {
  return new Intl.NumberFormat('zh-TW').format(price)
}

const increaseQuantity = (item) => {
  item.quantity++
}

const decreaseQuantity = (item) => {
  if (item.quantity > 1) {
    item.quantity--
  }
}

const removeItem = (item) => {
  const index = cartItems.value.findIndex(i => i.id === item.id)
  if (index > -1) {
    cartItems.value.splice(index, 1)
    message.success('已移除商品')
  }
}

const proceedToCheckout = () => {
  message.info('結帳功能開發中')
}

const goShopping = () => {
  router.push('/products')
}
</script>

<style scoped>
.mobile-check {
  background: #f8f9fa;
  min-height: calc(100vh - 130px);
}

.cart-header {
  background: white;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.cart-content {
  padding: 0;
}

.cart-items {
  background: white;
  margin-bottom: 8px;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f5f5f5;
  gap: 12px;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 4px;
}

.item-price {
  font-size: 14px;
  color: #dc3545;
  font-weight: 600;
  margin-bottom: 8px;
}

.item-quantity {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-btn {
  width: 28px;
  height: 28px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.quantity {
  min-width: 24px;
  text-align: center;
  font-weight: 500;
}

.remove-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
  color: #666;
}

.cart-summary {
  background: white;
  padding: 20px;
  margin-bottom: 8px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 16px;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-row.total {
  font-weight: 600;
  font-size: 18px;
  color: #dc3545;
  border-top: 1px solid #eee;
  padding-top: 12px;
}

.checkout-actions {
  padding: 20px;
}

.checkout-btn {
  width: 100%;
  padding: 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
}

.empty-cart {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
}

.shop-btn {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
}
</style> 