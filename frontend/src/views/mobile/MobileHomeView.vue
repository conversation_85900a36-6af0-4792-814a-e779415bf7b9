<template>
  <div class="mobile-home">
    <!-- 輪播區域 -->
    <div class="carousel-section">
      <div class="carousel-container" v-if="carouselData.length > 0">
        <div 
          class="carousel-track" 
          :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
        >
          <div 
            v-for="(item, index) in carouselData" 
            :key="index" 
            class="carousel-slide"
          >
            <img 
              :src="item.image" 
              :alt="item.title" 
              class="carousel-image"
              @error="handleImageError"
            />
          </div>
        </div>
        
        <!-- 輪播指示器 -->
        <div class="carousel-dots">
          <span 
            v-for="(item, index) in carouselData" 
            :key="index"
            class="dot"
            :class="{ active: currentSlide === index }"
            @click="goToSlide(index)"
          ></span>
        </div>
      </div>
    </div>
    
    <!-- 快速導航 -->
    <div class="quick-nav-section">
      <div class="quick-nav-grid">
        <router-link to="/products" class="quick-nav-item">
          <div class="nav-icon products-icon"></div>
          <span class="nav-text">所有商品</span>
        </router-link>
        
        <router-link to="/brands" class="quick-nav-item">
          <div class="nav-icon brands-icon"></div>
          <span class="nav-text">品牌專區</span>
        </router-link>
        
        <router-link to="/products?sort=社員專區" class="quick-nav-item" v-if="isUser">
          <div class="nav-icon member-icon"></div>
          <span class="nav-text">社員專區</span>
        </router-link>
        
        <router-link to="/news" class="quick-nav-item">
          <div class="nav-icon news-icon"></div>
          <span class="nav-text">最新消息</span>
        </router-link>
      </div>
    </div>
    
    <!-- 商品分類 -->
    <div class="categories-section" v-if="classSet.length > 0">
      <div class="section-header">
        <h2 class="section-title">商品分類</h2>
        <router-link to="/products" class="view-all-link">查看全部</router-link>
      </div>
      
      <div class="categories-grid">
        <router-link 
          v-for="category in displayCategories" 
          :key="category.id"
          :to="`/products?category=${category.id}`"
          class="category-item"
        >
          <div class="category-icon">
            <span class="category-name-initial">{{ category.name.charAt(0) }}</span>
          </div>
          <span class="category-name">{{ category.name }}</span>
        </router-link>
      </div>
    </div>
    
    <!-- 精選商品 -->
    <div class="featured-section" v-if="featuredProducts.length > 0">
      <div class="section-header">
        <h2 class="section-title">精選商品</h2>
        <router-link to="/products?featured=true" class="view-all-link">查看更多</router-link>
      </div>
      
      <div class="products-carousel">
        <div class="products-track" ref="productsTrack">
          <div 
            v-for="product in featuredProducts" 
            :key="product.id"
            class="product-card"
            @click="goToProduct(product.id)"
          >
            <div class="product-image-container">
              <img 
                :src="product.image || '/no-image.jpg'" 
                :alt="product.name"
                class="product-image"
                @error="handleProductImageError"
              />
              <div v-if="product.isNew" class="product-badge new">新品</div>
              <div v-if="product.isHot" class="product-badge hot">熱銷</div>
            </div>
            
            <div class="product-info">
              <h3 class="product-name">{{ product.name }}</h3>
              <div class="product-price">
                <span class="current-price">NT$ {{ formatPrice(product.price) }}</span>
                <span v-if="product.originalPrice" class="original-price">NT$ {{ formatPrice(product.originalPrice) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 品牌專區 -->
    <div class="brands-section" v-if="popularBrands.length > 0">
      <div class="section-header">
        <h2 class="section-title">熱門品牌</h2>
        <router-link to="/brands" class="view-all-link">查看全部</router-link>
      </div>
      
      <div class="brands-grid">
        <router-link 
          v-for="brand in popularBrands" 
          :key="brand.id"
          :to="`/brand/${brand.id}`"
          class="brand-item"
        >
          <div class="brand-logo">
            <img 
              :src="brand.logo || '/no-image.jpg'" 
              :alt="brand.name"
              class="brand-image"
              @error="handleBrandImageError"
            />
          </div>
          <span class="brand-name">{{ brand.name }}</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import api from '@/utils/api.js'

const router = useRouter()
const { getApiData } = api()
const handelPinia = piniaStores()
const { classSet, isUser } = storeToRefs(handelPinia)

// 輪播相關
const currentSlide = ref(0)
const carouselData = ref([])
const carouselInterval = ref(null)

// 商品和品牌數據
const featuredProducts = ref([])
const popularBrands = ref([])

// 計算屬性
const displayCategories = computed(() => {
  return classSet.value.slice(0, 8) // 只顯示前8個分類
})

// 輪播控制
const goToSlide = (index) => {
  currentSlide.value = index
}

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % carouselData.value.length
}

const startCarousel = () => {
  if (carouselData.value.length > 1) {
    carouselInterval.value = setInterval(nextSlide, 5000)
  }
}

const stopCarousel = () => {
  if (carouselInterval.value) {
    clearInterval(carouselInterval.value)
    carouselInterval.value = null
  }
}

// 導航到商品詳情
const goToProduct = (productId) => {
  router.push(`/product?id=${productId}`)
}

// 格式化價格
const formatPrice = (price) => {
  return new Intl.NumberFormat('zh-TW').format(price)
}

// 圖片錯誤處理
const handleImageError = (event) => {
  event.target.src = '/no-image.jpg'
}

const handleProductImageError = (event) => {
  event.target.src = '/no-image.jpg'
}

const handleBrandImageError = (event) => {
  event.target.src = '/no-image.jpg'
}

// 獲取輪播數據
const fetchCarouselData = async () => {
  try {
    const response = await getApiData('/api/carousel')
    if (response.success) {
      carouselData.value = response.data
    }
  } catch (error) {
    console.error('獲取輪播數據失敗:', error)
  }
}

// 獲取精選商品
const fetchFeaturedProducts = async () => {
  try {
    const response = await getApiData('/api/products?featured=true&limit=10')
    if (response.success) {
      featuredProducts.value = response.data
    }
  } catch (error) {
    console.error('獲取精選商品失敗:', error)
  }
}

// 獲取熱門品牌
const fetchPopularBrands = async () => {
  try {
    const response = await getApiData('/api/brands?popular=true&limit=6')
    if (response.success) {
      popularBrands.value = response.data
    }
  } catch (error) {
    console.error('獲取熱門品牌失敗:', error)
  }
}

// 初始化
onMounted(async () => {
  try {
    await Promise.all([
      fetchCarouselData(),
      fetchFeaturedProducts(),
      fetchPopularBrands()
    ])
    startCarousel()
  } catch (error) {
    console.error('初始化首頁數據失敗:', error)
  }
})

onBeforeUnmount(() => {
  stopCarousel()
})
</script>

<style scoped>
/* 手機版首頁樣式 */
.mobile-home {
  padding: 0;
  background: #f8f9fa;
}

/* 輪播區域 */
.carousel-section {
  position: relative;
  background: #fff;
}

.carousel-container {
  position: relative;
  overflow: hidden;
  height: 200px;
}

.carousel-track {
  display: flex;
  transition: transform 0.5s ease;
  height: 100%;
}

.carousel-slide {
  flex: none;
  width: 100%;
  height: 100%;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-dots {
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background 0.3s;
}

.dot.active {
  background: #fff;
}

/* 快速導航 */
.quick-nav-section {
  background: #fff;
  padding: 16px;
  margin-bottom: 8px;
}

.quick-nav-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.quick-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #333;
  padding: 12px 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.quick-nav-item:hover {
  background-color: #f5f5f5;
}

.nav-icon {
  width: 40px;
  height: 40px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-bottom: 8px;
  border-radius: 8px;
  background-color: #e3f2fd;
}

.nav-text {
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
}

/* 區塊標題 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fff;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.view-all-link {
  color: #007bff;
  text-decoration: none;
  font-size: 14px;
}

/* 商品分類 */
.categories-section {
  background: #fff;
  margin-bottom: 8px;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 0 16px 16px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #333;
  padding: 12px 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.category-item:hover {
  background-color: #f5f5f5;
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.category-name-initial {
  color: #fff;
  font-weight: 600;
  font-size: 16px;
}

.category-name {
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
}

/* 精選商品 */
.featured-section {
  background: #fff;
  margin-bottom: 8px;
}

.products-carousel {
  overflow-x: auto;
  padding: 0 16px 16px;
}

.products-track {
  display: flex;
  gap: 12px;
  padding-bottom: 8px;
}

.product-card {
  flex: none;
  width: 140px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s;
}

.product-card:hover {
  transform: translateY(-2px);
}

.product-image-container {
  position: relative;
  height: 140px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 2px 6px;
  font-size: 10px;
  border-radius: 4px;
  color: #fff;
  font-weight: 500;
}

.product-badge.new {
  background: #28a745;
}

.product-badge.hot {
  background: #dc3545;
}

.product-info {
  padding: 12px;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 0 0 8px;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #dc3545;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

/* 品牌專區 */
.brands-section {
  background: #fff;
  margin-bottom: 8px;
}

.brands-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding: 0 16px 16px;
}

.brand-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #333;
  padding: 16px 8px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  transition: all 0.2s;
}

.brand-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.brand-logo {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 8px;
  background: #f5f5f5;
}

.brand-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.brand-name {
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
}

/* 圖標定義 */
.products-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10'/%3E%3C/svg%3E");
}

.brands-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z'/%3E%3C/svg%3E");
}

.member-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z'/%3E%3C/svg%3E");
}

.news-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z'/%3E%3C/svg%3E");
}

/* 響應式設計 */
@media (max-width: 480px) {
  .quick-nav-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
  }
  
  .categories-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .brands-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .product-card {
    width: 120px;
  }
  
  .product-image-container {
    height: 120px;
  }
}

/* 自定義滾動條 */
.products-carousel::-webkit-scrollbar {
  height: 4px;
}

.products-carousel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.products-carousel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.products-carousel::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style> 