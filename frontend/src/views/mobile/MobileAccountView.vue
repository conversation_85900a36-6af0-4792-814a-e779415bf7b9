<template>
  <div class="mobile-account">
    <div class="account-header">
      <div class="user-avatar">
        <i class="user-icon"></i>
      </div>
      <div class="user-info">
        <h2 class="user-name">{{ userData.name || '用戶' }}</h2>
        <p class="user-email">{{ userData.email || '<EMAIL>' }}</p>
      </div>
    </div>
    
    <div class="account-menu">
      <div class="menu-group">
        <h3 class="group-title">個人資料</h3>
        <div class="menu-item" @click="editProfile">
          <i class="profile-icon"></i>
          <span>編輯個人資料</span>
          <i class="arrow-icon"></i>
        </div>
        <div class="menu-item" @click="changePassword">
          <i class="password-icon"></i>
          <span>變更密碼</span>
          <i class="arrow-icon"></i>
        </div>
      </div>
      
      <div class="menu-group">
        <h3 class="group-title">訂單管理</h3>
        <div class="menu-item" @click="viewOrders">
          <i class="order-icon"></i>
          <span>我的訂單</span>
          <i class="arrow-icon"></i>
        </div>
        <div class="menu-item" @click="viewWishlist">
          <i class="heart-icon"></i>
          <span>我的收藏</span>
          <i class="arrow-icon"></i>
        </div>
      </div>
      
      <div class="menu-group">
        <h3 class="group-title">其他</h3>
        <div class="menu-item" @click="contactSupport">
          <i class="support-icon"></i>
          <span>客服支援</span>
          <i class="arrow-icon"></i>
        </div>
        <div class="menu-item" @click="logout">
          <i class="logout-icon"></i>
          <span>登出</span>
          <i class="arrow-icon"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

const router = useRouter()
const userData = ref({
  name: '用戶',
  email: '<EMAIL>'
})

const editProfile = () => {
  message.info('編輯個人資料功能開發中')
}

const changePassword = () => {
  message.info('變更密碼功能開發中')
}

const viewOrders = () => {
  message.info('查看訂單功能開發中')
}

const viewWishlist = () => {
  message.info('查看收藏功能開發中')
}

const contactSupport = () => {
  message.info('客服支援功能開發中')
}

const logout = () => {
  message.success('已登出')
  router.push('/login')
}
</script>

<style scoped>
.mobile-account {
  background: #f8f9fa;
  min-height: calc(100vh - 130px);
}

.account-header {
  background: white;
  padding: 24px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-bottom: 1px solid #eee;
}

.user-avatar {
  width: 60px;
  height: 60px;
  background: #e3f2fd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-icon {
  width: 32px;
  height: 32px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.6;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px;
}

.user-email {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.account-menu {
  padding: 16px 0;
}

.menu-group {
  margin-bottom: 24px;
}

.group-title {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin: 0 0 12px;
  padding: 0 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-item {
  background: white;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #f8f9fa;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item i {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  margin-right: 12px;
  opacity: 0.6;
}

.menu-item span {
  flex: 1;
  font-size: 16px;
}

.arrow-icon {
  width: 16px !important;
  height: 16px !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 5l7 7-7 7'/%3E%3C/svg%3E") !important;
  margin-right: 0 !important;
  margin-left: 8px !important;
}

.profile-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'/%3E%3C/svg%3E");
}

.password-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'/%3E%3C/svg%3E");
}

.order-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'/%3E%3C/svg%3E");
}

.heart-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z'/%3E%3C/svg%3E");
}

.support-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z'/%3E%3C/svg%3E");
}

.logout-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1'/%3E%3C/svg%3E");
}
</style> 