<template>
  <div class="mobile-brands">
    <div class="page-header">
      <h1>品牌專區</h1>
    </div>
    
    <div class="brands-grid">
      <div v-for="brand in brands" :key="brand.id" class="brand-item" @click="goToBrand(brand.id)">
        <div class="brand-logo">
          <img :src="brand.logo || '/no-image.jpg'" :alt="brand.name" />
        </div>
        <h3 class="brand-name">{{ brand.name }}</h3>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const brands = ref([
  { id: 1, name: '品牌1', logo: '/no-image.jpg' },
  { id: 2, name: '品牌2', logo: '/no-image.jpg' },
  { id: 3, name: '品牌3', logo: '/no-image.jpg' },
  { id: 4, name: '品牌4', logo: '/no-image.jpg' }
])

const goToBrand = (brandId) => {
  router.push(`/brand/${brandId}`)
}
</script>

<style scoped>
.mobile-brands {
  background: #f8f9fa;
  min-height: calc(100vh - 130px);
}

.page-header {
  background: white;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.page-header h1 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.brands-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 16px;
}

.brand-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s;
}

.brand-item:hover {
  transform: translateY(-2px);
}

.brand-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto 12px;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
}

.brand-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.brand-name {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}
</style> 