<template>
  <div class="mobile-brand-detail">
    <div class="brand-header">
      <div class="brand-logo">
        <img :src="brand.logo || '/no-image.jpg'" :alt="brand.name" />
      </div>
      <h1 class="brand-name">{{ brand.name }}</h1>
      <p class="brand-description">{{ brand.description }}</p>
    </div>
    
    <div class="brand-products">
      <h2 class="section-title">品牌商品</h2>
      <div class="products-grid">
        <div v-for="product in products" :key="product.id" class="product-card" @click="goToProduct(product.id)">
          <div class="product-image">
            <img :src="product.image || '/no-image.jpg'" :alt="product.name" />
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <div class="product-price">NT$ {{ formatPrice(product.price) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const brand = ref({
  id: 1,
  name: '品牌名稱',
  logo: '/no-image.jpg',
  description: '這是一個優質的品牌。'
})

const products = ref([
  { id: 1, name: '商品1', price: 1000, image: '/no-image.jpg' },
  { id: 2, name: '商品2', price: 2000, image: '/no-image.jpg' }
])

const formatPrice = (price) => {
  return new Intl.NumberFormat('zh-TW').format(price)
}

const goToProduct = (productId) => {
  router.push(`/product?id=${productId}`)
}

onMounted(() => {
  const brandId = route.params.id || 'all'
  brand.value.name = brandId === 'all' ? '所有品牌' : `品牌 ${brandId}`
})
</script>

<style scoped>
.mobile-brand-detail {
  background: #f8f9fa;
  min-height: calc(100vh - 130px);
}

.brand-header {
  background: white;
  text-align: center;
  padding: 24px 20px;
  border-bottom: 1px solid #eee;
}

.brand-logo {
  width: 100px;
  height: 100px;
  margin: 0 auto 16px;
  border-radius: 12px;
  overflow: hidden;
  background: #f5f5f5;
}

.brand-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.brand-name {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px;
}

.brand-description {
  color: #666;
  margin: 0;
}

.brand-products {
  padding: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.product-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s;
}

.product-card:hover {
  transform: translateY(-2px);
}

.product-image {
  aspect-ratio: 1;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 12px;
}

.product-name {
  font-size: 14px;
  margin: 0 0 8px;
}

.product-price {
  font-size: 16px;
  font-weight: 600;
  color: #dc3545;
}
</style> 