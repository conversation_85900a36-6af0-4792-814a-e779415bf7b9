<template>
  <div class="mobile-login">
    <div class="login-container">
      <!-- 登入表單標題 -->
      <div class="login-header">
        <h1 class="login-title">會員登入</h1>
        <p class="login-subtitle">歡迎回來！請登入您的帳號</p>
      </div>

      <!-- 登入表單 -->
      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label for="email" class="form-label">電子郵件</label>
          <input
            v-model="loginForm.email"
            type="email"
            id="email"
            class="form-input"
            placeholder="請輸入您的電子郵件"
            required
            :disabled="loading"
          />
        </div>

        <div class="form-group">
          <label for="password" class="form-label">密碼</label>
          <div class="password-input-container">
            <input
              v-model="loginForm.password"
              :type="showPassword ? 'text' : 'password'"
              id="password"
              class="form-input"
              placeholder="請輸入您的密碼"
              required
              :disabled="loading"
            />
            <button
              type="button"
              @click="togglePassword"
              class="password-toggle"
              :disabled="loading"
            >
              <i :class="showPassword ? 'eye-off-icon' : 'eye-icon'"></i>
            </button>
          </div>
        </div>

        <div class="form-options">
          <label class="remember-checkbox">
            <input
              v-model="loginForm.rememberMe"
              type="checkbox"
              :disabled="loading"
            />
            <span class="checkbox-custom"></span>
            <span class="checkbox-label">記住我</span>
          </label>
          
          <a href="#" @click.prevent="showForgotPassword = true" class="forgot-link">
            忘記密碼？
          </a>
        </div>

        <button
          type="submit"
          class="login-btn"
          :disabled="loading || !isFormValid"
        >
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? '登入中...' : '登入' }}
        </button>
      </form>

      <!-- 分隔線 -->
      <div class="divider">
        <span class="divider-text">或</span>
      </div>

      <!-- 註冊提示 -->
      <div class="register-prompt">
        <p class="register-text">還沒有帳號？</p>
        <button @click="showRegister = true" class="register-btn">
          立即註冊
        </button>
      </div>

      <!-- 其他登入方式 -->
      <div class="social-login">
        <p class="social-text">其他登入方式</p>
        <div class="social-buttons">
          <button @click="handleGoogleLogin" class="social-btn google-btn" :disabled="loading">
            <i class="google-icon"></i>
            Google
          </button>
          <button @click="handleFacebookLogin" class="social-btn facebook-btn" :disabled="loading">
            <i class="facebook-icon"></i>
            Facebook
          </button>
        </div>
      </div>
    </div>

    <!-- 註冊彈窗 -->
    <div v-if="showRegister" class="modal-overlay" @click="closeRegister">
      <div class="register-modal" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">會員註冊</h2>
          <button @click="closeRegister" class="close-btn">×</button>
        </div>
        
        <form @submit.prevent="handleRegister" class="register-form">
          <div class="form-group">
            <label for="reg-name" class="form-label">姓名</label>
            <input
              v-model="registerForm.name"
              type="text"
              id="reg-name"
              class="form-input"
              placeholder="請輸入您的姓名"
              required
              :disabled="registerLoading"
            />
          </div>

          <div class="form-group">
            <label for="reg-email" class="form-label">電子郵件</label>
            <input
              v-model="registerForm.email"
              type="email"
              id="reg-email"
              class="form-input"
              placeholder="請輸入您的電子郵件"
              required
              :disabled="registerLoading"
            />
          </div>

          <div class="form-group">
            <label for="reg-password" class="form-label">密碼</label>
            <input
              v-model="registerForm.password"
              type="password"
              id="reg-password"
              class="form-input"
              placeholder="請輸入密碼（至少6位）"
              required
              minlength="6"
              :disabled="registerLoading"
            />
          </div>

          <div class="form-group">
            <label for="reg-confirm-password" class="form-label">確認密碼</label>
            <input
              v-model="registerForm.confirmPassword"
              type="password"
              id="reg-confirm-password"
              class="form-input"
              placeholder="請再次輸入密碼"
              required
              :disabled="registerLoading"
            />
          </div>

          <div class="form-group">
            <label class="agree-checkbox">
              <input
                v-model="registerForm.agreeTerms"
                type="checkbox"
                required
                :disabled="registerLoading"
              />
              <span class="checkbox-custom"></span>
              <span class="checkbox-label">
                我同意
                <a href="/privacy" target="_blank" class="terms-link">服務條款</a>
                和
                <a href="/privacy" target="_blank" class="terms-link">隱私政策</a>
              </span>
            </label>
          </div>

          <button
            type="submit"
            class="register-submit-btn"
            :disabled="registerLoading || !isRegisterFormValid"
          >
            <span v-if="registerLoading" class="loading-spinner"></span>
            {{ registerLoading ? '註冊中...' : '註冊' }}
          </button>
        </form>
      </div>
    </div>

    <!-- 忘記密碼彈窗 -->
    <div v-if="showForgotPassword" class="modal-overlay" @click="closeForgotPassword">
      <div class="forgot-modal" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">重設密碼</h2>
          <button @click="closeForgotPassword" class="close-btn">×</button>
        </div>
        
        <form @submit.prevent="handleForgotPassword" class="forgot-form">
          <p class="forgot-description">
            請輸入您的電子郵件地址，我們將發送重設密碼的連結給您。
          </p>
          
          <div class="form-group">
            <label for="forgot-email" class="form-label">電子郵件</label>
            <input
              v-model="forgotForm.email"
              type="email"
              id="forgot-email"
              class="form-input"
              placeholder="請輸入您的電子郵件"
              required
              :disabled="forgotLoading"
            />
          </div>

          <button
            type="submit"
            class="forgot-submit-btn"
            :disabled="forgotLoading || !forgotForm.email"
          >
            <span v-if="forgotLoading" class="loading-spinner"></span>
            {{ forgotLoading ? '發送中...' : '發送重設連結' }}
          </button>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import { message } from 'ant-design-vue'
import api from '@/utils/api.js'

const router = useRouter()
const route = useRoute()
const { postApiData } = api()
const handelPinia = piniaStores()
const { isUser } = storeToRefs(handelPinia)

// 狀態管理
const loading = ref(false)
const registerLoading = ref(false)
const forgotLoading = ref(false)
const showPassword = ref(false)
const showRegister = ref(false)
const showForgotPassword = ref(false)

// 表單數據
const loginForm = ref({
  email: '',
  password: '',
  rememberMe: false
})

const registerForm = ref({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

const forgotForm = ref({
  email: ''
})

// 計算屬性
const isFormValid = computed(() => {
  return loginForm.value.email && loginForm.value.password
})

const isRegisterFormValid = computed(() => {
  return registerForm.value.name &&
         registerForm.value.email &&
         registerForm.value.password &&
         registerForm.value.confirmPassword &&
         registerForm.value.password === registerForm.value.confirmPassword &&
         registerForm.value.agreeTerms
})

// 密碼顯示切換
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 登入處理
const handleLogin = async () => {
  if (!isFormValid.value) return

  loading.value = true
  try {
    const response = await postApiData('/api/auth/login', {
      email: loginForm.value.email,
      password: loginForm.value.password,
      rememberMe: loginForm.value.rememberMe
    })

    if (response.success) {
      // 儲存登入資料
      localStorage.setItem('token', response.token)
      localStorage.setItem('isLogin', 'true')
      if (response.user) {
        localStorage.setItem('userData', JSON.stringify(response.user))
      }

      // 更新 Pinia 狀態
      handelPinia.initUser(true)
      
      message.success('登入成功！')

      // 重導向
      const redirect = route.query.redirect || '/'
      router.push(redirect)
    } else {
      message.error(response.message || '登入失敗，請檢查您的帳號密碼')
    }
  } catch (error) {
    console.error('登入錯誤:', error)
    message.error('登入失敗，請稍後再試')
  } finally {
    loading.value = false
  }
}

// 註冊處理
const handleRegister = async () => {
  if (!isRegisterFormValid.value) return

  if (registerForm.value.password !== registerForm.value.confirmPassword) {
    message.error('密碼確認不符')
    return
  }

  registerLoading.value = true
  try {
    const response = await postApiData('/api/auth/register', {
      name: registerForm.value.name,
      email: registerForm.value.email,
      password: registerForm.value.password
    })

    if (response.success) {
      message.success('註冊成功！請登入您的帳號')
      closeRegister()
      
      // 自動填入登入表單
      loginForm.value.email = registerForm.value.email
      loginForm.value.password = ''
    } else {
      message.error(response.message || '註冊失敗，請稍後再試')
    }
  } catch (error) {
    console.error('註冊錯誤:', error)
    message.error('註冊失敗，請稍後再試')
  } finally {
    registerLoading.value = false
  }
}

// 忘記密碼處理
const handleForgotPassword = async () => {
  if (!forgotForm.value.email) return

  forgotLoading.value = true
  try {
    const response = await postApiData('/api/auth/forgot-password', {
      email: forgotForm.value.email
    })

    if (response.success) {
      message.success('重設密碼連結已發送到您的郵箱')
      closeForgotPassword()
    } else {
      message.error(response.message || '發送失敗，請檢查郵箱地址')
    }
  } catch (error) {
    console.error('忘記密碼錯誤:', error)
    message.error('發送失敗，請稍後再試')
  } finally {
    forgotLoading.value = false
  }
}

// 社交登入
const handleGoogleLogin = () => {
  message.info('Google 登入功能開發中')
}

const handleFacebookLogin = () => {
  message.info('Facebook 登入功能開發中')
}

// 彈窗控制
const closeRegister = () => {
  showRegister.value = false
  registerForm.value = {
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false
  }
}

const closeForgotPassword = () => {
  showForgotPassword.value = false
  forgotForm.value.email = ''
}

// 檢查登入狀態
onMounted(() => {
  if (isUser.value) {
    const redirect = route.query.redirect || '/'
    router.push(redirect)
  }
})
</script>

<style scoped>
/* 手機版登入頁面樣式 */
.mobile-login {
  min-height: calc(100vh - 130px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
  background: #fff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 登入標題 */
.login-header {
  text-align: center;
  margin-bottom: 24px;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px;
}

.login-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 表單樣式 */
.login-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: #666;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.remember-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
}

.remember-checkbox input {
  display: none;
}

.checkbox-custom {
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-radius: 3px;
  margin-right: 8px;
  position: relative;
  transition: all 0.2s;
}

.remember-checkbox input:checked + .checkbox-custom {
  background-color: #667eea;
  border-color: #667eea;
}

.remember-checkbox input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
}

.forgot-link {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
}

.forgot-link:hover {
  text-decoration: underline;
}

.login-btn {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: opacity 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 分隔線 */
.divider {
  position: relative;
  text-align: center;
  margin: 24px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #eee;
}

.divider-text {
  background: #fff;
  padding: 0 16px;
  color: #666;
  font-size: 14px;
}

/* 註冊提示 */
.register-prompt {
  text-align: center;
  margin-bottom: 24px;
}

.register-text {
  font-size: 14px;
  color: #666;
  margin: 0 0 8px;
}

.register-btn {
  background: none;
  border: 1px solid #667eea;
  color: #667eea;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.register-btn:hover {
  background: #667eea;
  color: white;
}

/* 社交登入 */
.social-login {
  text-align: center;
}

.social-text {
  font-size: 14px;
  color: #666;
  margin: 0 0 16px;
}

.social-buttons {
  display: flex;
  gap: 12px;
}

.social-btn {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;
}

.social-btn:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.google-btn:hover {
  border-color: #4285f4;
  color: #4285f4;
}

.facebook-btn:hover {
  border-color: #1877f2;
  color: #1877f2;
}

/* 彈窗樣式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.register-modal,
.forgot-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: #f5f5f5;
}

.register-form,
.forgot-form {
  padding: 24px;
}

.register-submit-btn,
.forgot-submit-btn {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: opacity 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.register-submit-btn:disabled,
.forgot-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.agree-checkbox {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  font-size: 14px;
  line-height: 1.4;
}

.agree-checkbox input {
  display: none;
}

.agree-checkbox .checkbox-custom {
  margin-top: 2px;
  flex-shrink: 0;
}

.terms-link {
  color: #667eea;
  text-decoration: none;
}

.terms-link:hover {
  text-decoration: underline;
}

.forgot-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

/* 圖標樣式 */
.eye-icon,
.eye-off-icon,
.google-icon,
.facebook-icon {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.eye-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 12a3 3 0 11-6 0 3 3 0 016 0z'/%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z'/%3E%3C/svg%3E");
}

.eye-off-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21'/%3E%3C/svg%3E");
}

.google-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285f4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334a853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23fbbc05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23ea4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E");
}

.facebook-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231877f2'%3E%3Cpath d='M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z'/%3E%3C/svg%3E");
}

/* 響應式設計 */
@media (max-width: 480px) {
  .mobile-login {
    padding: 16px;
  }
  
  .login-container {
    padding: 20px;
  }
  
  .modal-overlay {
    padding: 16px;
  }
  
  .social-buttons {
    flex-direction: column;
  }
}
</style> 