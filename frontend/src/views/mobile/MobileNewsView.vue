<template>
  <div class="mobile-news">
    <div class="page-header">
      <h1>最新消息</h1>
    </div>
    
    <div class="news-list">
      <div v-for="news in newsList" :key="news.id" class="news-item">
        <div class="news-date">{{ news.date }}</div>
        <h3 class="news-title">{{ news.title }}</h3>
        <p class="news-summary">{{ news.summary }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const newsList = ref([
  {
    id: 1,
    title: '新商品上架通知',
    summary: '本週新商品已經上架，歡迎選購！',
    date: '2024-01-15'
  },
  {
    id: 2,
    title: '春節假期營業時間調整',
    summary: '春節期間營業時間將有所調整，請注意！',
    date: '2024-01-10'
  }
])
</script>

<style scoped>
.mobile-news {
  background: #f8f9fa;
  min-height: calc(100vh - 130px);
}

.page-header {
  background: white;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.page-header h1 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.news-list {
  padding: 16px;
}

.news-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 12px;
}

.news-date {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.news-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px;
}

.news-summary {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}
</style> 