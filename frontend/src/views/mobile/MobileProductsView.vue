<template>
  <div class="mobile-products">
    <div class="search-bar">
      <input 
        v-model="searchQuery" 
        type="text" 
        placeholder="搜尋商品..." 
        class="search-input"
      />
    </div>
    
    <div class="products-grid">
      <div 
        v-for="product in products" 
        :key="product.id"
        class="product-card"
        @click="goToProduct(product.id)"
      >
        <div class="product-image">
          <img :src="product.image || '/no-image.jpg'" :alt="product.name" />
        </div>
        <div class="product-info">
          <h3 class="product-name">{{ product.name }}</h3>
          <div class="product-price">NT$ {{ formatPrice(product.price) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const searchQuery = ref('')
const products = ref([
  { id: 1, name: '商品1', price: 1000, image: '/no-image.jpg' },
  { id: 2, name: '商品2', price: 2000, image: '/no-image.jpg' }
])

const goToProduct = (productId) => {
  router.push(`/product?id=${productId}`)
}

const formatPrice = (price) => {
  return new Intl.NumberFormat('zh-TW').format(price)
}
</script>

<style scoped>
.mobile-products {
  padding: 16px;
  background: #f8f9fa;
  min-height: calc(100vh - 130px);
}

.search-bar {
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.product-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.product-image {
  aspect-ratio: 1;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 12px;
}

.product-name {
  font-size: 14px;
  margin: 0 0 8px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  font-size: 16px;
  font-weight: 600;
  color: #dc3545;
}
</style> 