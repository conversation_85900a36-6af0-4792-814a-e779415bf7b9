<template>
  <div class="mobile-products">
    <!-- 搜索和篩選欄 -->
    <div class="search-filter-bar">
      <div class="search-container">
        <input 
          v-model="searchQuery" 
          @input="handleSearch"
          type="text" 
          placeholder="搜尋商品..." 
          class="search-input"
        />
        <i class="search-icon"></i>
      </div>
      
      <button @click="showFilterModal = true" class="filter-btn">
        <i class="filter-icon"></i>
        篩選
      </button>
    </div>
    
    <!-- 分類標籤 -->
    <div class="category-tabs" v-if="classSet.length > 0">
      <div class="tabs-container">
        <button 
          @click="selectCategory(null)"
          class="category-tab"
          :class="{ active: !selectedCategory }"
        >
          全部
        </button>
        <button 
          v-for="category in classSet" 
          :key="category.id"
          @click="selectCategory(category.id)"
          class="category-tab"
          :class="{ active: selectedCategory === category.id }"
        >
          {{ category.name }}
        </button>
      </div>
    </div>
    
    <!-- 排序和視圖切換 -->
    <div class="toolbar">
      <div class="sort-dropdown">
        <select v-model="sortBy" @change="handleSort" class="sort-select">
          <option value="default">預設排序</option>
          <option value="price_asc">價格低到高</option>
          <option value="price_desc">價格高到低</option>
          <option value="name_asc">名稱 A-Z</option>
          <option value="date_desc">最新商品</option>
        </select>
      </div>
      
      <div class="view-toggle">
        <button 
          @click="viewMode = 'grid'"
          class="view-btn"
          :class="{ active: viewMode === 'grid' }"
        >
          <i class="grid-icon"></i>
        </button>
        <button 
          @click="viewMode = 'list'"
          class="view-btn"
          :class="{ active: viewMode === 'list' }"
        >
          <i class="list-icon"></i>
        </button>
      </div>
    </div>
    
    <!-- 商品列表 -->
    <div class="products-container">
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">載入中...</p>
      </div>
      
      <div v-else-if="products.length === 0" class="empty-container">
        <div class="empty-icon"></div>
        <p class="empty-text">找不到相關商品</p>
        <button @click="clearFilters" class="clear-filters-btn">清除篩選</button>
      </div>
      
      <div v-else :class="['products-grid', viewMode]">
        <div 
          v-for="product in products" 
          :key="product.id"
          class="product-card"
          @click="goToProduct(product.id)"
        >
          <div class="product-image-container">
            <img 
              :src="product.image || '/no-image.jpg'" 
              :alt="product.name"
              class="product-image"
              @error="handleImageError"
            />
            <div v-if="product.isNew" class="product-badge new">新品</div>
            <div v-if="product.isHot" class="product-badge hot">熱銷</div>
            <div v-if="product.discount" class="product-badge discount">{{ product.discount }}折</div>
          </div>
          
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <p v-if="viewMode === 'list'" class="product-description">{{ product.description }}</p>
            
            <div class="product-price">
              <span class="current-price">NT$ {{ formatPrice(product.price) }}</span>
              <span v-if="product.originalPrice" class="original-price">
                NT$ {{ formatPrice(product.originalPrice) }}
              </span>
            </div>
            
            <div v-if="viewMode === 'list'" class="product-actions">
              <button @click.stop="addToCart(product)" class="add-cart-btn">
                <i class="cart-icon"></i>
                加入購物車
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 載入更多 -->
      <div v-if="hasMore && !loading" class="load-more-container">
        <button @click="loadMore" class="load-more-btn" :disabled="loadingMore">
          {{ loadingMore ? '載入中...' : '載入更多' }}
        </button>
      </div>
    </div>
    
    <!-- 篩選彈窗 -->
    <div v-if="showFilterModal" class="filter-modal-overlay" @click="closeFilterModal">
      <div class="filter-modal" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">商品篩選</h3>
          <button @click="closeFilterModal" class="close-btn">×</button>
        </div>
        
        <div class="filter-content">
          <!-- 價格範圍 -->
          <div class="filter-group">
            <h4 class="filter-title">價格範圍</h4>
            <div class="price-range">
              <input 
                v-model="filters.minPrice" 
                type="number" 
                placeholder="最低價"
                class="price-input"
              />
              <span class="price-separator">-</span>
              <input 
                v-model="filters.maxPrice" 
                type="number" 
                placeholder="最高價"
                class="price-input"
              />
            </div>
          </div>
          
          <!-- 品牌篩選 -->
          <div class="filter-group">
            <h4 class="filter-title">品牌</h4>
            <div class="brand-list">
              <label v-for="brand in brands" :key="brand.id" class="brand-checkbox">
                <input 
                  v-model="filters.selectedBrands" 
                  :value="brand.id" 
                  type="checkbox"
                />
                <span class="checkbox-custom"></span>
                <span class="brand-name">{{ brand.name }}</span>
              </label>
            </div>
          </div>
          
          <!-- 其他篩選條件 */
          <div class="filter-group">
            <h4 class="filter-title">其他條件</h4>
            <div class="filter-options">
              <label class="filter-checkbox">
                <input v-model="filters.inStock" type="checkbox" />
                <span class="checkbox-custom"></span>
                <span class="checkbox-label">僅顯示有庫存</span>
              </label>
              <label class="filter-checkbox">
                <input v-model="filters.onSale" type="checkbox" />
                <span class="checkbox-custom"></span>
                <span class="checkbox-label">特價商品</span>
              </label>
            </div>
          </div>
        </div>
        
        <div class="filter-actions">
          <button @click="clearFilters" class="clear-btn">清除</button>
          <button @click="applyFilters" class="apply-btn">套用</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import { message } from 'ant-design-vue'
import api from '@/utils/api.js'

const router = useRouter()
const route = useRoute()
const { getApiData, postApiData } = api()
const handelPinia = piniaStores()
const { classSet, carts } = storeToRefs(handelPinia)

// 狀態管理
const loading = ref(false)
const loadingMore = ref(false)
const showFilterModal = ref(false)
const products = ref([])
const brands = ref([])
const hasMore = ref(true)
const currentPage = ref(1)

// 搜索和篩選
const searchQuery = ref('')
const selectedCategory = ref(null)
const sortBy = ref('default')
const viewMode = ref('grid')

const filters = ref({
  minPrice: '',
  maxPrice: '',
  selectedBrands: [],
  inStock: false,
  onSale: false
})

// 搜索處理
const handleSearch = () => {
  currentPage.value = 1
  loadProducts()
}

// 分類選擇
const selectCategory = (categoryId) => {
  selectedCategory.value = categoryId
  currentPage.value = 1
  loadProducts()
}

// 排序處理
const handleSort = () => {
  currentPage.value = 1
  loadProducts()
}

// 導航到商品詳情
const goToProduct = (productId) => {
  router.push(`/product?id=${productId}`)
}

// 加入購物車
const addToCart = async (product) => {
  try {
    await handelPinia.addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1
    })
    message.success('已加入購物車')
  } catch (error) {
    console.error('加入購物車失敗:', error)
    message.error('加入購物車失敗')
  }
}

// 載入商品
const loadProducts = async (append = false) => {
  if (!append) {
    loading.value = true
  } else {
    loadingMore.value = true
  }

  try {
    const params = {
      page: currentPage.value,
      limit: 20,
      search: searchQuery.value,
      category: selectedCategory.value,
      sort: sortBy.value,
      ...filters.value
    }

    const response = await getApiData('/api/products', { params })
    
    if (response.success) {
      if (append) {
        products.value = [...products.value, ...response.data]
      } else {
        products.value = response.data
      }
      
      hasMore.value = response.hasMore || false
    }
  } catch (error) {
    console.error('載入商品失敗:', error)
    message.error('載入商品失敗')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 載入更多
const loadMore = () => {
  currentPage.value++
  loadProducts(true)
}

// 載入品牌列表
const loadBrands = async () => {
  try {
    const response = await getApiData('/api/brands')
    if (response.success) {
      brands.value = response.data
    }
  } catch (error) {
    console.error('載入品牌失敗:', error)
  }
}

// 篩選處理
const closeFilterModal = () => {
  showFilterModal.value = false
}

const clearFilters = () => {
  filters.value = {
    minPrice: '',
    maxPrice: '',
    selectedBrands: [],
    inStock: false,
    onSale: false
  }
  selectedCategory.value = null
  searchQuery.value = ''
  currentPage.value = 1
  loadProducts()
  closeFilterModal()
}

const applyFilters = () => {
  currentPage.value = 1
  loadProducts()
  closeFilterModal()
}

// 工具函數
const formatPrice = (price) => {
  return new Intl.NumberFormat('zh-TW').format(price)
}

const handleImageError = (event) => {
  event.target.src = '/no-image.jpg'
}

// 監聽路由參數變化
watch(() => route.query, (newQuery) => {
  if (newQuery.category) {
    selectedCategory.value = parseInt(newQuery.category)
  }
  if (newQuery.search) {
    searchQuery.value = newQuery.search
  }
  if (newQuery.sort) {
    sortBy.value = newQuery.sort
  }
  currentPage.value = 1
  loadProducts()
}, { immediate: true })

// 初始化
onMounted(async () => {
  await Promise.all([
    loadProducts(),
    loadBrands()
  ])
})
</script>

<style scoped>
/* 手機版商品列表樣式 */
.mobile-products {
  background: #f8f9fa;
  min-height: calc(100vh - 130px);
}

/* 搜索和篩選欄 */
.search-filter-bar {
  display: flex;
  gap: 8px;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;
}

.search-container {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 10px 16px 10px 40px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
  background: #f5f5f5;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.5;
}

.filter-btn {
  padding: 10px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 分類標籤 */
.category-tabs {
  background: #fff;
  border-bottom: 1px solid #eee;
  padding: 8px 0;
}

.tabs-container {
  display: flex;
  gap: 8px;
  padding: 0 16px;
  overflow-x: auto;
}

.category-tab {
  padding: 8px 16px;
  background: #f5f5f5;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  white-space: nowrap;
  transition: all 0.2s;
}

.category-tab.active {
  background: #007bff;
  color: white;
}

/* 工具欄 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;
}

.sort-select {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.view-toggle {
  display: flex;
  gap: 4px;
}

.view-btn {
  padding: 6px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  transition: all 0.2s;
}

.view-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* 商品列表 */
.products-container {
  padding: 16px;
}

.products-grid.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.products-grid.list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.product-card {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s;
}

.product-card:hover {
  transform: translateY(-2px);
}

.products-grid.list .product-card {
  display: flex;
  gap: 12px;
  padding: 12px;
}

.product-image-container {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
}

.products-grid.list .product-image-container {
  width: 100px;
  height: 100px;
  flex-shrink: 0;
  border-radius: 8px;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 2px 6px;
  font-size: 10px;
  border-radius: 4px;
  color: white;
  font-weight: 500;
}

.product-badge.new {
  background: #28a745;
}

.product-badge.hot {
  background: #dc3545;
}

.product-badge.discount {
  background: #ffc107;
  color: #333;
}

.product-info {
  padding: 12px;
}

.products-grid.list .product-info {
  flex: 1;
  padding: 0;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 8px;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-description {
  font-size: 12px;
  color: #666;
  margin: 0 0 8px;
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #dc3545;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.product-actions {
  margin-top: auto;
}

.add-cart-btn {
  width: 100%;
  padding: 8px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

/* 載入狀態 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top-color: #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 16px;
  color: #666;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 空狀態 */
.empty-container {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  background: #f5f5f5;
  border-radius: 50%;
}

.empty-text {
  color: #666;
  margin-bottom: 16px;
}

.clear-filters-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
}

/* 載入更多 */
.load-more-container {
  text-align: center;
  padding: 20px;
}

.load-more-btn {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 篩選彈窗 */
.filter-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: flex-end;
}

.filter-modal {
  background: white;
  width: 100%;
  max-height: 80vh;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
}

.filter-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.filter-group {
  margin-bottom: 24px;
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px;
}

.price-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
}

.price-separator {
  color: #666;
}

.brand-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.brand-checkbox,
.filter-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.brand-checkbox input,
.filter-checkbox input {
  display: none;
}

.checkbox-custom {
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-radius: 3px;
  margin-right: 8px;
  position: relative;
  transition: all 0.2s;
}

.brand-checkbox input:checked + .checkbox-custom,
.filter-checkbox input:checked + .checkbox-custom {
  background: #007bff;
  border-color: #007bff;
}

.brand-checkbox input:checked + .checkbox-custom::after,
.filter-checkbox input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-actions {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #eee;
}

.clear-btn {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 6px;
  font-size: 14px;
}

.apply-btn {
  flex: 1;
  padding: 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
}

/* 響應式設計 */
@media (max-width: 480px) {
  .products-grid.grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .products-container {
    padding: 12px;
  }
}
</style> 