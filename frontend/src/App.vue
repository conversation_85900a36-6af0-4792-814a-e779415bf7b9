<template>
  <!-- 根據設備類型顯示不同的應用組件 -->
  <component :is="currentAppComponent" />
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { storeToRefs } from 'pinia'
import piniaStores from '@/stores/apiStore'
import { isMobileDevice, watchDeviceChange } from '@/utils/deviceDetection'

// 電腦版組件
import HeaderItem from '@/components/HeaderItem.vue'
import FooterItem from '@/components/FooterItem.vue'

// 手機版組件
import MobileApp from '@/MobileApp.vue'

// 初始化 Pinia store
const handelPinia = piniaStores()
const { classSet, isUser, carts, total } = storeToRefs(handelPinia)

// 設備檢測
const isMobile = ref(isMobileDevice())
const cleanupDeviceWatcher = ref(null)

// 根據設備類型決定使用的應用組件
const currentAppComponent = computed(() => {
  return isMobile.value ? MobileApp : DesktopApp
})

// 電腦版應用組件
const DesktopApp = {
  name: 'DesktopApp',
  components: {
    HeaderItem,
    FooterItem
  },
  template: `
    <div id="desktop-app">
      <HeaderItem />
      <main class="main-content">
        <router-view />
      </main>
      <FooterItem />
    </div>
  `
}

// 設備變化處理
const handleDeviceChange = (newIsMobile) => {
  if (isMobile.value !== newIsMobile) {
    isMobile.value = newIsMobile
    console.log(`設備類型變更為: ${newIsMobile ? '手機' : '電腦'}`)
  }
}

// 在應用啟動時預加載分類數據並檢查登入狀態
onMounted(async () => {
  console.log(`當前設備類型: ${isMobile.value ? '手機' : '電腦'}`)
  
  try {
    // 直接載入分類數據，不需要先清空
    await handelPinia.getClassSet()
    console.log('App: 分類數據加載完成，共', classSet.value.length, '項')
    
    // 檢查用戶登入狀態
    const token = localStorage.getItem('token')
    const isLogin = localStorage.getItem('isLogin')
    
    // 如果用戶未登入但購物車有數據，則清空購物車
    if ((!token || !isLogin) && carts.value.length > 0) {
      console.log('App: 用戶未登入，清空購物車')
      handelPinia.clearCart()
    } else {
      // 初始化用戶狀態
      handelPinia.initUser(true)
    }
  } catch (error) {
    console.error('App: 預加載分類數據失敗', error)
  }

  // 設置設備變化監聽器
  cleanupDeviceWatcher.value = watchDeviceChange(handleDeviceChange)
})

onBeforeUnmount(() => {
  // 清理設備變化監聽器
  if (cleanupDeviceWatcher.value) {
    cleanupDeviceWatcher.value()
  }
})
</script>

<style>
/* 全局樣式 */
html, body {
  margin: 0;
  padding: 0;
  font-family: "Microsoft JhengHei", "PingFang TC", "Heiti TC", sans-serif;
  width: 100%;
  height: 100%;
  overflow-x: hidden; /* 防止水平滾動 */
}

#app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden; /* 防止水平滾動 */
}

/* 電腦版樣式 */
#desktop-app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden;
}

/* 主內容區域樣式 */
.main-content {
  padding-top: 80px; /* 固定頂部間距，適應 header 高度 */
  flex: 1;
  width: 100%;
  position: relative;
  z-index: 1; /* 確保內容在正確的層級 */
}

/* 確保下拉選單在所有頁面正確顯示 */
.dropdown-menu {
  z-index: 1001; /* 設置比header更高的z-index */
}

/* 全局組件樣式優先級 */
header {
  z-index: 1000 !important; /* 確保header永遠在最頂層 */
}

/* 禁止頁面元素覆蓋固定的header */
.fixed, .absolute {
  z-index: auto;
}

/* 手機版特有樣式調整 */
@media (max-width: 768px) {
  .main-content {
    padding-top: 60px; /* 手機版header較小 */
  }
}

/* 設備切換時的過渡動畫 */
#app {
  transition: all 0.3s ease;
}

/* 確保在不同設備間切換時的平滑體驗 */
* {
  box-sizing: border-box;
}

/* 針對觸控設備的優化 */
@media (hover: none) and (pointer: coarse) {
  /* 移除hover效果，改善觸控體驗 */
  *:hover {
    background-color: inherit !important;
  }
  
  /* 增加觸控目標的最小尺寸 */
  button, a, input, select, textarea {
    min-height: 44px;
  }
}

/* 高分辨率螢幕優化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* 高DPI顯示優化 */
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 深色模式支持（預留） */
@media (prefers-color-scheme: dark) {
  /* 未來可以在這裡添加深色模式樣式 */
}

/* 減少動畫的用戶偏好 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 確保可訪問性 */
/* 焦點樣式 */
*:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* 跳過連結（無障礙功能） */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 9999;
}

.skip-link:focus {
  top: 6px;
}

/* 螢幕閱讀器專用內容 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 確保圖片在不同設備上的顯示 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 載入狀態的全局樣式 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 全局按鈕重置 */
button {
  cursor: pointer;
  border: none;
  background: none;
  font-family: inherit;
}

/* 全局連結樣式 */
a {
  color: inherit;
  text-decoration: none;
}

/* 表單元素的全局樣式 */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

/* 確保在iOS Safari上的正確顯示 */
input[type="search"] {
  -webkit-appearance: none;
}

/* 滾動條樣式（webkit瀏覽器） */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Firefox滾動條樣式 */
* {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}
</style>