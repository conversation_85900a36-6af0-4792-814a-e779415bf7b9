// 設備檢測工具
export const isMobileDevice = () => {
  // 檢查螢幕寬度
  const screenWidth = window.innerWidth
  if (screenWidth <= 768) return true
  
  // 檢查用戶代理字符串
  const userAgent = navigator.userAgent || navigator.vendor || window.opera
  
  // 手機設備正則表達式
  const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i
  
  // 觸控設備檢測
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  
  // 組合檢測結果
  return mobileRegex.test(userAgent) || (isTouchDevice && screenWidth <= 1024)
}

// 監聽螢幕尺寸變化
export const watchDeviceChange = (callback) => {
  let timeoutId = null
  
  const handleResize = () => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      callback(isMobileDevice())
    }, 300) // 延遲執行，避免頻繁觸發
  }
  
  window.addEventListener('resize', handleResize)
  
  // 返回清理函數
  return () => {
    window.removeEventListener('resize', handleResize)
    clearTimeout(timeoutId)
  }
}

// 獲取設備類型
export const getDeviceType = () => {
  return isMobileDevice() ? 'mobile' : 'desktop'
}

// 設備方向檢測
export const getOrientation = () => {
  return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'
} 